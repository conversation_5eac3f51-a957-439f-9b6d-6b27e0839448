const ListingService = require('../../../services/listing.service');
const { logger } = require('../../../utils/logger');

class ListingController {
  constructor(container = null) {
    this.listingService = new ListingService();
    this.container = container;

    // Initialize view service if container is available
    if (container) {
      try {
        this.listingViewService = container.resolve('listingViewService');
      } catch (error) {
        logger.warn('ListingView service not available:', error.message);
        this.listingViewService = null;
      }
    }
  }

  // Create a new listing
  async createListing(request, reply) {
    try {
      const userId = request.user?.id;
      console.log('User ID from request:', userId);
      
      if (!userId) {
        return reply.code(401).send({
          success: false,
          message: 'Authentication required',
          error: 'User ID not found in request'
        });
      }

      const listingData = request.body;
      console.log('Listing data received:', listingData);

      // Create the listing
      const listing = await this.listingService.createListing(listingData, userId);

      return reply.code(201).send({
        success: true,
        message: 'Listing created successfully',
        data: listing
      });
    } catch (error) {
      logger.error('Error in createListing controller:', error);
      return reply.code(400).send({
        success: false,
        message: error.message || 'Failed to create listing',
        error: error.message
      });
    }
  }

  // Get listing by ID
  async getListingById(request, reply) {
    try {
      const { id } = request.params;
      const { incrementView = false } = request.query;

      const listing = await this.listingService.getListingById(id, incrementView);

      return reply.send({
        success: true,
        data: listing
      });
    } catch (error) {
      logger.error('Error in getListingById controller:', error);
      const statusCode = error.message === 'Listing not found' ? 404 : 500;
      return reply.code(statusCode).send({
        success: false,
        message: error.message || 'Failed to get listing',
        error: error.message
      });
    }
  }

  // Get all listings with filters
  async getAllListings(request, reply) {
    try {
      const options = request.query;
      const result = await this.listingService.getAllListings(options);

      return reply.send({
        success: true,
        data: result.listings,
        pagination: result.pagination
      });
    } catch (error) {
      logger.error('Error in getAllListings controller:', error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to get listings',
        error: error.message
      });
    }
  }

  // Get listings by category
  async getListingsByCategory(request, reply) {
    try {
      const { id: categoryId } = request.params;
      const options = request.query;

      const result = await this.listingService.getListingsByCategory(categoryId, options);

      return reply.send({
        success: true,
        data: result.listings,
        pagination: result.pagination
      });
    } catch (error) {
      logger.error('Error in getListingsByCategory controller:', error);
      const statusCode = error.message === 'Category not found' ? 404 : 500;
      return reply.code(statusCode).send({
        success: false,
        message: error.message || 'Failed to get listings by category',
        error: error.message
      });
    }
  }

  // Get listings by subcategory
  async getListingsBySubcategory(request, reply) {
    try {
      const { id: subcategoryId } = request.params;
      const options = request.query;

      const result = await this.listingService.getListingsBySubcategory(subcategoryId, options);

      return reply.send({
        success: true,
        data: result.listings,
        pagination: result.pagination
      });
    } catch (error) {
      logger.error('Error in getListingsBySubcategory controller:', error);
      const statusCode = error.message === 'Subcategory not found' ? 404 : 500;
      return reply.code(statusCode).send({
        success: false,
        message: error.message || 'Failed to get listings by subcategory',
        error: error.message
      });
    }
  }

  // Search listings
  async searchListings(request, reply) {
    try {
      const searchOptions = request.query;
      const result = await this.listingService.searchListings(searchOptions);

      return reply.send({
        success: true,
        data: result.listings,
        pagination: result.pagination
      });
    } catch (error) {
      logger.error('Error in searchListings controller:', error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to search listings',
        error: error.message
      });
    }
  }

  // Get related listings
  async getRelatedListings(request, reply) {
    try {
      const { id } = request.params;
      const { limit = 6 } = request.query;

      const relatedListings = await this.listingService.getRelatedListings(id, limit);

      return reply.send({
        success: true,
        data: relatedListings
      });
    } catch (error) {
      logger.error('Error in getRelatedListings controller:', error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to get related listings',
        error: error.message
      });
    }
  }

  // Get featured listings
  async getFeaturedListings(request, reply) {
    try {
      const options = request.query;
      const result = await this.listingService.getFeaturedListings(options);

      return reply.send({
        success: true,
        data: result.listings,
        pagination: result.pagination
      });
    } catch (error) {
      logger.error('Error in getFeaturedListings controller:', error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to get featured listings',
        error: error.message
      });
    }
  }

  // Update listing
  async updateListing(request, reply) {
    try {
      const { id } = request.params;
      const userId = request.user?.id; 
      const updateData = request.body;

      const updatedListing = await this.listingService.updateListing(id, updateData, userId);

      return reply.send({
        success: true,
        message: 'Listing updated successfully',
        data: updatedListing
      });
    } catch (error) {
      logger.error('Error in updateListing controller:', error);
      const statusCode = error.message.includes('not found') ? 404 :
        error.message.includes('Unauthorized') ? 403 : 400;
      return reply.code(statusCode).send({
        success: false,
        message: error.message || 'Failed to update listing',
        error: error.message
      });
    }
  }

  // Delete listing
  async deleteListing(request, reply) {
    try {
      const { id } = request.params;
      const userId = request.user?.id; 

      await this.listingService.deleteListing(id, userId);

      return reply.send({
        success: true,
        message: 'Listing deleted successfully'
      });
    } catch (error) {
      logger.error('Error in deleteListing controller:', error);
      const statusCode = error.message.includes('not found') ? 404 :
        error.message.includes('Unauthorized') ? 403 : 500;
      return reply.code(statusCode).send({
        success: false,
        message: error.message || 'Failed to delete listing',
        error: error.message
      });
    }
  }

  // Get user's listings
  async getUserListings(request, reply) {
    try {
      const userId = request.user?.id; // For testing without auth
      const options = request.query;

      const result = await this.listingService.getUserListings(userId, options);

      return reply.send({
        success: true,
        data: result.listings,
        pagination: result.pagination
      });
    } catch (error) {
      logger.error('Error in getUserListings controller:', error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to get user listings',
        error: error.message
      });
    }
  }

  // Toggle listing availability
  async toggleListingAvailability(request, reply) {
    try {
      const { id } = request.params;
      const userId = request.user?.id; // For testing without auth

      const updatedListing = await this.listingService.toggleListingAvailability(id, userId);

      return reply.send({
        success: true,
        message: 'Listing availability updated successfully',
        data: updatedListing
      });
    } catch (error) {
      logger.error('Error in toggleListingAvailability controller:', error);
      const statusCode = error.message.includes('not found') ? 404 :
        error.message.includes('Unauthorized') ? 403 : 500;
      return reply.code(statusCode).send({
        success: false,
        message: error.message || 'Failed to toggle listing availability',
        error: error.message
      });
    }
  }

  // Get all categories
  async getAllCategories(request, reply) {
    try {
      const categories = await this.listingService.getAllCategories();

      return reply.send({
        success: true,
        data: categories
      });
    } catch (error) {
      logger.error('Error in getAllCategories controller:', error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to get categories',
        error: error.message
      });
    }
  }

  // Get category by ID
  async getCategoryById(request, reply) {
    try {
      const { id } = request.params;
      const category = await this.listingService.getCategoryById(id);

      return reply.send({
        success: true,
        data: category
      });
    } catch (error) {
      logger.error('Error in getCategoryById controller:', error);
      const statusCode = error.message === 'Category not found' ? 404 : 500;
      return reply.code(statusCode).send({
        success: false,
        message: error.message || 'Failed to get category',
        error: error.message
      });
    }
  }

  // Get subcategories by category
  async getSubcategoriesByCategory(request, reply) {
    try {
      const { id: categoryId } = request.params;
      const subcategories = await this.listingService.getSubcategoriesByCategory(categoryId);

      return reply.send({
        success: true,
        data: subcategories
      });
    } catch (error) {
      logger.error('Error in getSubcategoriesByCategory controller:', error);
      const statusCode = error.message === 'Category not found' ? 404 : 500;
      return reply.code(statusCode).send({
        success: false,
        message: error.message || 'Failed to get subcategories',
        error: error.message
      });
    }
  }

  // Home page API - Get categories with their data
  async getHomePageData(request, reply) {
    try {
      const options = request.query;
      const result = await this.listingService.getHomePageData(options);

      return reply.send({
        success: true,
        data: result
      });
    } catch (error) {
      logger.error('Error in getHomePageData controller:', error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to get home page data',
        error: error.message
      });
    }
  }

  // Category page API - Get category with subcategories and all category data
  async getCategoryPageData(request, reply) {
    try {
      const { id: categoryId } = request.params;
      const options = request.query;

      const result = await this.listingService.getCategoryPageData(categoryId, options);

      return reply.send({
        success: true,
        data: result
      });
    } catch (error) {
      logger.error('Error in getCategoryPageData controller:', error);
      const statusCode = error.message === 'Category not found' ? 404 : 500;
      return reply.code(statusCode).send({
        success: false,
        message: error.message || 'Failed to get category page data',
        error: error.message
      });
    }
  }

  // Track view for a listing
  async trackListingView(request, reply) {
    try {
      if (!this.listingViewService) {
        return reply.code(503).send({
          success: false,
          message: 'View tracking service not available'
        });
      }

      const { id: listingId } = request.params;
      const userId = request.user?.id || null;

      // Extract request metadata
      const ipAddress = request.ip || request.headers['x-forwarded-for'] || request.connection.remoteAddress;
      const userAgent = request.headers['user-agent'];
      const referrer = request.headers.referer || request.headers.referrer;

      // Get additional data from request body
      const { sessionId, deviceType } = request.body || {};

      // Detect device type if not provided
      const detectedDeviceType = deviceType || this.listingViewService.detectDeviceType(userAgent);

      // Generate session ID for anonymous users if not provided
      const finalSessionId = sessionId || (!userId ? this.listingViewService.generateSessionId(ipAddress, userAgent) : null);

      const requestData = {
        userId,
        ipAddress,
        userAgent,
        sessionId: finalSessionId,
        referrer,
        deviceType: detectedDeviceType
      };

      const result = await this.listingViewService.trackView(listingId, requestData);

      return reply.send({
        success: true,
        data: result
      });

    } catch (error) {
      logger.error('Error in trackListingView controller:', error);
      const statusCode = error.message === 'Listing not found' ? 404 : 500;
      return reply.code(statusCode).send({
        success: false,
        message: error.message || 'Failed to track view',
        error: error.message
      });
    }
  }

  // Get view statistics for a listing
  async getListingViewStatistics(request, reply) {
    try {
      if (!this.listingViewService) {
        return reply.code(503).send({
          success: false,
          message: 'View tracking service not available'
        });
      }

      const { id: listingId } = request.params;
      const { days = 30 } = request.query;

      const statistics = await this.listingViewService.getViewStatistics(listingId, parseInt(days));

      return reply.send({
        success: true,
        data: statistics
      });

    } catch (error) {
      logger.error('Error in getListingViewStatistics controller:', error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to get view statistics',
        error: error.message
      });
    }
  }

  // Get daily view counts for a listing
  async getListingDailyViews(request, reply) {
    try {
      if (!this.listingViewService) {
        return reply.code(503).send({
          success: false,
          message: 'View tracking service not available'
        });
      }

      const { id: listingId } = request.params;
      const { days = 7 } = request.query;

      const dailyViews = await this.listingViewService.getDailyViewCounts(listingId, parseInt(days));

      return reply.send({
        success: true,
        data: dailyViews
      });

    } catch (error) {
      logger.error('Error in getListingDailyViews controller:', error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to get daily view counts',
        error: error.message
      });
    }
  }

  // Get unique view count for a listing
  async getListingUniqueViewCount(request, reply) {
    try {
      if (!this.listingViewService) {
        return reply.code(503).send({
          success: false,
          message: 'View tracking service not available'
        });
      }

      const { id: listingId } = request.params;

      const uniqueViewCount = await this.listingViewService.getUniqueViewCount(listingId);

      return reply.send({
        success: true,
        data: {
          listingId,
          uniqueViewCount
        }
      });

    } catch (error) {
      logger.error('Error in getListingUniqueViewCount controller:', error);
      return reply.code(500).send({
        success: false,
        message: error.message || 'Failed to get unique view count',
        error: error.message
      });
    }
  }
}

module.exports = ListingController;
